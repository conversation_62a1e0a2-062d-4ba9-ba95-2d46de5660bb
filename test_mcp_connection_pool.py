#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MCP连接池功能
"""

import asyncio
import logging
import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.mcp_server_manager import MCPServerManager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_connection_pool():
    """测试连接池功能"""
    
    # 创建MCP服务器管理器
    manager = MCPServerManager()
    
    # 模拟多次工具调用，测试连接复用
    server_id = "variables-server"  # 使用内部服务器进行测试
    tool_name = "get_variable"
    params = {"name": "test_var"}
    
    logger.info("开始测试连接池...")
    
    # 第一次调用 - 应该创建新连接
    logger.info("=== 第一次工具调用 ===")
    result1 = await manager.call_tool_async(server_id, tool_name, params)
    logger.info(f"第一次调用结果: {result1}")
    
    # 第二次调用 - 应该复用连接
    logger.info("=== 第二次工具调用 ===")
    result2 = await manager.call_tool_async(server_id, tool_name, params)
    logger.info(f"第二次调用结果: {result2}")
    
    # 第三次调用 - 应该复用连接
    logger.info("=== 第三次工具调用 ===")
    result3 = await manager.call_tool_async(server_id, tool_name, params)
    logger.info(f"第三次调用结果: {result3}")
    
    # 检查连接池状态
    pool_size = len(manager.connection_pool._connections)
    logger.info(f"连接池中的连接数: {pool_size}")
    
    # 清理
    await manager.connection_pool.close_all()
    logger.info("测试完成，已清理所有连接")

if __name__ == "__main__":
    asyncio.run(test_connection_pool())
